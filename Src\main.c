/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "adc.h"
#include "dma.h"
#include "usart.h"
#include "rtc.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "e70_config.h"
#include <string.h>
#include <stdio.h>
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */
// 母设备业务流程控制变量
static uint8_t e70_first_init_done = 0;        // E70首次初始化标志
static uint32_t e70_wake_start_time = 0;       // E70唤醒开始时间
static uint8_t e70_wake_retry_count = 0;       // E70唤醒重试次数
static uint16_t saved_battery_voltage = 0;     // 保存的电池电压值
static uint16_t slave_device_id = 0;           // 子设备ID
static uint16_t slave_battery_voltage = 0;     // 子设备电池电压
static uint8_t interrupt_enabled = 1;          // 中断使能标志，1=允许响应，0=禁止响应

// 通信测试模式控制变量
// 修改这个值来切换模式：1=通信测试模式，0=正常状态机模式
static uint8_t communication_test_mode = 1;    // 通信测试模式标志，1=测试模式，0=正常状态机模式
static uint32_t last_send_time = 0;            // 上次发送时间

// 母设备业务流程状态枚举
typedef enum {
    MASTER_STATE_WAKE_UP = 0,           // 唤醒阶段
    MASTER_STATE_E70_WAKE_SLAVE,        // 唤醒子设备阶段
    MASTER_STATE_WAIT_SLAVE_DATA,       // 等待子设备数据阶段
    MASTER_STATE_SEND_ACK,              // 发送确认阶段
    MASTER_STATE_VOLTAGE_CHECK,         // 电压检测阶段（E70完成后）
    MASTER_STATE_CAT1_GPS,              // CAT1 GPS数据获取阶段（预留）
    MASTER_STATE_CAT1_UPLOAD,           // CAT1上传数据阶段（预留）
    MASTER_STATE_PREPARE_SLEEP          // 准备休眠阶段
} MasterState_t;

static MasterState_t current_state = MASTER_STATE_WAKE_UP;
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */
uint8_t txmp[6]={0xC0,0x00,0x00,0x18,0x04,0x1C};
uint8_t rxmp[10];
/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_USART1_UART_Init();
  MX_ADC_Init();
  MX_RTC_Init();
  MX_LPUART1_UART_Init();
  MX_USART4_UART_Init();
  /* USER CODE BEGIN 2 */
  printf("=== STM32L071 Master Device Low Power System ===\r\n");
  LED_ON;

  // E70首次初始化（生命周期内只执行一次）
  if (!e70_first_init_done) {
    printf("=== E70 First Time Initialization ===\r\n");
    HAL_GPIO_WritePin(GPIOA, M0_Pin|M1_Pin|M2_Pin|RF_PWR_Pin|CAT1_PWR_Pin, GPIO_PIN_RESET);
    uint8_t init_result = E70_InitializeConfig(E70_MODULE_ADDRESS, 10, 1);
    if (!init_result) {
      printf("E70 Init FAILED!\r\n");
      Error_Handler();
    }
    E70_EnterCommMode();
    printf("=== E70 Configuration Complete ===\r\n");
    e70_first_init_done = 1;  // 标记首次初始化完成
  }

  // 初始化时关闭所有模块电源
  RF_PWR_OFF;   // 关闭E70模块
  CAT1_PWR_OFF; // 关闭CAT1模块

  // 启用GPIO中断 - INT1和INT2用于唤醒
  HAL_NVIC_SetPriority(EXTI4_15_IRQn, 0, 0);
  HAL_NVIC_EnableIRQ(EXTI4_15_IRQn);

  // 启用UART中断 - 确保E70通信正常
  HAL_NVIC_SetPriority(LPUART1_IRQn, 3, 0);
  HAL_NVIC_EnableIRQ(LPUART1_IRQn);
  HAL_NVIC_SetPriority(USART1_IRQn, 3, 0);
  HAL_NVIC_EnableIRQ(USART1_IRQn);

  printf("Master Device Ready\r\n");

  // 显示当前工作模式
  if (communication_test_mode) {
    printf("=== COMMUNICATION TEST MODE ===\r\n");
  } else {
    printf("=== NORMAL STATE MACHINE MODE ===\r\n");
  }

  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */

    // ==================== 通信测试模式 ====================
    if (communication_test_mode) {
        static uint8_t test_initialized = 0;

        // 初始化测试模式（只执行一次）
        if (!test_initialized) {
            printf("Communication Test Mode Started\r\n");

            // 确保E70模块处于通信模式
            E70_EnterCommMode();
            HAL_Delay(100);

            // 启动接收
            E70_StartContinuousRx();

            test_initialized = 1;
            last_send_time = HAL_GetTick();
        }

        // 检查是否收到新数据
        extern volatile uint8_t new_data_received;
        extern volatile uint8_t uart_rx_buffer[];
        extern volatile uint16_t uart_rx_index;

        static uint32_t last_status_time = 0;
        if ((HAL_GetTick() - last_status_time) > 3000) {
            printf("Status: new_data_received=%d, uart_rx_index=%d\r\n", new_data_received, uart_rx_index);
            last_status_time = HAL_GetTick();
        }

        if (new_data_received) {
            printf("RX (%d bytes): ", uart_rx_index);
            for (int i = 0; i < uart_rx_index; i++) {
                printf("%02X ", uart_rx_buffer[i]);
            }
            printf("\r\n");

            // 清空标志，重置接收状态
            new_data_received = 0;
            uart_rx_index = 0;
        }

        // 每1秒发送一次测试数据
        if ((HAL_GetTick() - last_send_time) >= 1000) {
            uint8_t test_packet[] = {0xFC, 0xFD, 0x55, 0x66, 0xFD};
            HAL_UART_Transmit(&hlpuart1, test_packet, sizeof(test_packet), 1000);
            printf("Sent: FC FD 55 66 FD\r\n");

            last_send_time = HAL_GetTick();
        }

        HAL_Delay(10);
        continue;
    }

    // ==================== 母设备业务流程状态机 ====================

    // 调试信息：显示当前状态
    static MasterState_t last_printed_state = (MasterState_t)255;  // 初始化为无效值
    if (current_state != last_printed_state) {
        printf("State Machine: Entering state %d\r\n", current_state);
        last_printed_state = current_state;
    }

    switch (current_state) {

        case MASTER_STATE_WAKE_UP:
        {
            printf("\r\n=== Master Device Wake Up ===\r\n");

            // 重置数据变量
            slave_device_id = 0;
            slave_battery_voltage = 0;
            saved_battery_voltage = 0;

            // 直接转到E70唤醒子设备阶段，优先处理通信
            current_state = MASTER_STATE_E70_WAKE_SLAVE;
            e70_wake_retry_count = 0;  // 重置重试计数
            break;
        }

        case MASTER_STATE_E70_WAKE_SLAVE:
        {
            printf("=== E70 Wake Slave Phase ===\r\n");

            // 开启E70电源
            printf("Powering ON E70 module...\r\n");
            E70_EnterCommMode();  // 这会断电重启E70模块，重置所有UART状态

            // E70断电重启后，需要重新初始化所有接收相关状态
            printf("Reinitializing UART receive after E70 power cycle...\r\n");

            // 等待E70模块完全稳定（断电重启需要更长时间）
            HAL_Delay(500);

            // 重新初始化接收状态变量
            extern volatile uint16_t uart_rx_index;
            extern volatile uint8_t new_data_received;
            uart_rx_index = 0;
            new_data_received = 0;

            // 重新启动接收
            E70_StartContinuousRx();
            printf("E70 Ready for Communication\r\n");

            // 发送唤醒信号: FC FD + 55 66 + FD
            printf("Sending wake signal to slave device...\r\n");
            uint8_t wake_packet[] = {0xFC, 0xFD, 0x55, 0x66, 0xFD};
            HAL_UART_Transmit(&hlpuart1, wake_packet, sizeof(wake_packet), 10);

            // 立即检查一次是否有数据（子设备可能在我们启动接收前就发送了）
            extern volatile uint8_t new_data_received;
            if (new_data_received) {
//                printf("Data already received during startup!\r\n");
            }

            e70_wake_start_time = HAL_GetTick();
            current_state = MASTER_STATE_WAIT_SLAVE_DATA;
            break;
        }

        case MASTER_STATE_WAIT_SLAVE_DATA:
        {
            // 使用和测试代码完全相同的接收检查逻辑
            extern volatile uint8_t new_data_received;
            extern volatile uint8_t uart_rx_buffer[];
            extern volatile uint16_t uart_rx_index;

            // 移除调试信息，让接收检查更快速

            // 检查数据接收标志
            extern volatile uint8_t data_received_flag;
            extern volatile uint8_t uart_rx_buffer[];
            extern volatile uint8_t actual_data_length;

            // 每2秒显示一次接收状态
            static uint32_t last_debug_time = 0;
            if ((HAL_GetTick() - last_debug_time) > 2000) {
                printf("Waiting for data... flag=%d\r\n", data_received_flag);
                last_debug_time = HAL_GetTick();
            }

            if (data_received_flag) {
                // 直接打印接收到的原始数据，支持7或8字节
                printf("State machine RX (%d bytes): ", actual_data_length);
                for (int i = 0; i < actual_data_length; i++) {
                    printf("%02X ", uart_rx_buffer[i]);
                }
                printf("\r\n");

                printf("Data received! Moving to ACK phase\r\n");

                // 停止接收并进入下一状态
                E70_StopContinuousRx();
                current_state = MASTER_STATE_SEND_ACK;
                break;  // 立即跳出，进入下一状态
            }

            // 检查是否需要发送唤醒信号（每1秒发送一次，最多6次）
            if ((HAL_GetTick() - e70_wake_start_time) > 1000) {
                e70_wake_retry_count++;
                printf("Wake timeout. Retry count: %d\r\n", e70_wake_retry_count);

                if (e70_wake_retry_count < 10) {
                    // 重新发送唤醒信号
                    printf("Retrying wake signal...\r\n");
                    uint8_t wake_packet[] = {0xFC, 0xFD, 0x55, 0x66, 0xFD};
                    HAL_UART_Transmit(&hlpuart1, wake_packet, sizeof(wake_packet), 10);
                    e70_wake_start_time = HAL_GetTick();
                } else {
                    // 6次重试后仍无响应，关闭E70，继续后续流程
                    printf("Max retries reached. No slave response.\r\n");
                    E70_StopContinuousRx();  // 确保停止接收
                    RF_PWR_OFF;

                    // 即使没有收到子设备数据，也继续执行后续流程
                    printf("Continuing to voltage check phase...\r\n");
                    current_state = MASTER_STATE_VOLTAGE_CHECK;
                }
            }

            break;
        }

        case MASTER_STATE_SEND_ACK:
        {
            printf("=== Send ACK Phase ===\r\n");

            // 确保接收已停止，避免接收其他子设备数据
            E70_StopContinuousRx();

            // 发送确认信号: FC FD + 77 88 + FD
            printf("Sending ACK to first slave device...\r\n");
            uint8_t ack_packet[] = {0xFC, 0xFD, 0x77, 0x88, 0xFD};
            HAL_UART_Transmit(&hlpuart1, ack_packet, sizeof(ack_packet), 1000);

            printf("E70 Communication completed successfully!\r\n");
            printf("Slave Device Data:\r\n");
            printf("- First Slave ID: 0x%04X\r\n", slave_device_id);
            printf("- First Slave Battery: %dmV\r\n", slave_battery_voltage);

            // 关闭E70模块电源
            RF_PWR_OFF;

            // 转到电压检测阶段
            current_state = MASTER_STATE_VOLTAGE_CHECK;
            break;
        }

        case MASTER_STATE_VOLTAGE_CHECK:
        {
            printf("=== Voltage Check Phase ===\r\n");
						RF_PWR_OFF;
            // E70模块已关闭，等待1秒让电源稳定，避免模块工作电流影响电压测量
            printf("Waiting for power stabilization after E70 shutdown...\r\n");
            HAL_Delay(1000);

            // 读取电池电压值并保存备用
            saved_battery_voltage = ADC_ReadBatteryVoltage();
            float battery_voltage_v = saved_battery_voltage / 1000.0f;
            printf("Master Battery Voltage: %.3fV (%dmV) - Saved\r\n", battery_voltage_v, saved_battery_voltage);

            // 转到CAT1 GPS阶段（暂时跳过，直接进入休眠）
            // current_state = MASTER_STATE_CAT1_GPS;  // 后续启用
            current_state = MASTER_STATE_PREPARE_SLEEP;  // 暂时直接休眠
            break;
        }

        case MASTER_STATE_CAT1_GPS:
        {
            printf("=== CAT1 GPS Phase ===\r\n");
            // TODO: 实现CAT1模块GPS数据获取
            printf("CAT1 GPS data acquisition (Not implemented yet)\r\n");

            // 转到CAT1上传阶段
            current_state = MASTER_STATE_CAT1_UPLOAD;
            break;
        }

        case MASTER_STATE_CAT1_UPLOAD:
        {
            printf("=== CAT1 Upload Phase ===\r\n");
            // TODO: 实现CAT1模块数据上传
            printf("CAT1 data upload to server (Not implemented yet)\r\n");

            // 工作完成，转到休眠阶段
            current_state = MASTER_STATE_PREPARE_SLEEP;
            break;
        }

        case MASTER_STATE_PREPARE_SLEEP:
        {
            printf("=== Prepare Sleep Phase ===\r\n");

//            // 显示完整的数据摘要
            printf("Work Cycle Summary:\r\n");
            printf("- Master Battery: %dmV\r\n", saved_battery_voltage);
            printf("- Slave ID: 0x%04X\r\n", slave_device_id);
            printf("- Slave Battery: %dmV\r\n", slave_battery_voltage);
            // printf("- GPS Data: (Not implemented)\r\n");
            // printf("- Upload Status: (Not implemented)\r\n");

            printf("Entering sleep mode...\r\n");

            // 进入休眠模式处理
            goto enter_sleep_mode;
        }

        default:
        {
            printf("ERROR: Unknown state %d\r\n", current_state);
            current_state = MASTER_STATE_PREPARE_SLEEP;
            break;
        }
    }

    // 状态机执行完一个状态后，继续循环执行下一个状态
    continue;

    enter_sleep_mode:
    // 进入休眠模式处理
    {
        // 声明GPIO配置结构体，用于休眠配置
        GPIO_InitTypeDef GPIO_InitStruct = {0};

        // ==================== 进入休眠模式 ====================
        printf("Preparing to enter STOP mode...\r\n");
        printf("Device will wake up on INT1 (PB5) or INT2 (PA8) interrupt\r\n");

        // 重新使能中断响应标志，准备下次唤醒
        interrupt_enabled = 1;
        printf("Interrupt response re-enabled for next wake cycle\r\n");

        HAL_Delay(500); // 让串口输出完成

        // 关闭所有外部模块电源
        RF_PWR_OFF;   // 关闭E70模块
        CAT1_PWR_OFF; // 关闭CAT1模块
        LED_OFF;      // 关闭LED

        // 停止并禁用UART中断
        HAL_UART_AbortReceive_IT(&huart1);
        HAL_UART_AbortReceive_IT(&hlpuart1);
        HAL_UART_AbortReceive_IT(&huart4);

        // 完全停止ADC和DMA
        HAL_ADC_Stop_DMA(&hadc);
        HAL_ADC_Stop(&hadc);
        if(hadc.DMA_Handle != NULL) {
            HAL_DMA_Abort(hadc.DMA_Handle);
        }



        // 将M0、M1、M2配置为输入模式（避免输出驱动消耗功耗）
        GPIO_InitStruct.Pin = M0_Pin | M1_Pin | M2_Pin;
        GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
        GPIO_InitStruct.Pull = GPIO_PULLDOWN;  // 下拉到低电平
        HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

        // 将LPUART1引脚配置为输入模式
        GPIO_InitStruct.Pin = GPIO_PIN_2 | GPIO_PIN_3;  // PA2(TX), PA3(RX)
        GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
        GPIO_InitStruct.Pull = GPIO_PULLDOWN;  // 下拉到低电平
        HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);



        // 关闭外设时钟
        __HAL_RCC_USART1_CLK_DISABLE();
        __HAL_RCC_LPUART1_CLK_DISABLE();
        __HAL_RCC_USART4_CLK_DISABLE();
        __HAL_RCC_ADC1_CLK_DISABLE();
        __HAL_RCC_DMA1_CLK_DISABLE();

        // 确保UART传输完成
        HAL_Delay(50);

        // 禁用所有中断，准备进入低功耗模式
        __disable_irq();

        // 禁用SysTick中断
        SysTick->CTRL &= ~SysTick_CTRL_TICKINT_Msk;

        // 禁用所有中断，只保留EXTI4_15唤醒中断
        for (uint8_t i = 0; i < 8; i++) {
            NVIC->ICER[i] = 0xFFFFFFFF;
        }

        // 确保EXTI4_15中断被启用（INT1和INT2唤醒中断）
        HAL_NVIC_SetPriority(EXTI4_15_IRQn, 0, 0);
        HAL_NVIC_EnableIRQ(EXTI4_15_IRQn);

        // 清除所有挂起的中断
        for (uint8_t i = 0; i < 8; i++) {
            NVIC->ICPR[i] = 0xFFFFFFFF;
        }

        // 清除PWR标志
        __HAL_PWR_CLEAR_FLAG(PWR_FLAG_WU);

        // 重新启用全局中断，但只有EXTI4_15中断处于活动状态
        __enable_irq();

        // 进入STOP模式，使用低功耗调节器
        HAL_PWR_EnterSTOPMode(PWR_LOWPOWERREGULATOR_ON, PWR_STOPENTRY_WFI);

        // ==================== 唤醒后恢复 ====================

        // 打开LED表示唤醒开始
        LED_ON;

        // 重新配置系统时钟 - STOP模式后必需
        SystemClock_Config();

        // 重新启用SysTick中断
        SysTick->CTRL |= SysTick_CTRL_TICKINT_Msk;

        // 重新启用必要的外设时钟
        __HAL_RCC_USART1_CLK_ENABLE();
        __HAL_RCC_LPUART1_CLK_ENABLE();
        __HAL_RCC_USART4_CLK_ENABLE();
        __HAL_RCC_ADC1_CLK_ENABLE();
        __HAL_RCC_DMA1_CLK_ENABLE();

        // 重新配置GPIO（恢复正常功能）
        MX_GPIO_Init();

        // 重新初始化外设
        MX_USART1_UART_Init();
        MX_LPUART1_UART_Init();
        MX_USART4_UART_Init();
        MX_ADC_Init();

        // 重新启用必要的中断
        HAL_NVIC_SetPriority(USART1_IRQn, 3, 0);
        HAL_NVIC_EnableIRQ(USART1_IRQn);
        HAL_NVIC_SetPriority(LPUART1_IRQn, 3, 0);
        HAL_NVIC_EnableIRQ(LPUART1_IRQn);
        // USART4只用于调试输出，不需要中断

        // 确保UART完全重新初始化
        HAL_Delay(50);

        // 重新启用GPIO中断
        HAL_NVIC_SetPriority(EXTI4_15_IRQn, 0, 0);
        HAL_NVIC_EnableIRQ(EXTI4_15_IRQn);



        // 恢复M0、M1、M2为输出模式
        GPIO_InitStruct.Pin = M0_Pin | M1_Pin | M2_Pin;
        GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
        GPIO_InitStruct.Pull = GPIO_NOPULL;
        GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
        HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

        // 设置M0、M1、M2为低电平
        HAL_GPIO_WritePin(GPIOA, M0_Pin | M1_Pin | M2_Pin, GPIO_PIN_RESET);

        printf("Communication module pins restored\r\n");

        // LED闪烁表示正在唤醒
        LED_TOGGLE;
        HAL_Delay(100);
        LED_TOGGLE;
        HAL_Delay(100);
        LED_TOGGLE;
        HAL_Delay(100);
        LED_ON;

        printf("\r\n*** MASTER WAKE UP from STOP mode ***\r\n");

        // 重置状态机到唤醒状态
        current_state = MASTER_STATE_WAKE_UP;

    } // 结束 enter_sleep_mode 代码块

  }
  /* USER CODE END 3 */
}


/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
  RCC_PeriphCLKInitTypeDef PeriphClkInit = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI|RCC_OSCILLATORTYPE_LSI;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
  RCC_OscInitStruct.LSIState = RCC_LSI_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSI;
  RCC_OscInitStruct.PLL.PLLMUL = RCC_PLLMUL_4;
  RCC_OscInitStruct.PLL.PLLDIV = RCC_PLLDIV_4;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_0) != HAL_OK)
  {
    Error_Handler();
  }
  PeriphClkInit.PeriphClockSelection = RCC_PERIPHCLK_USART1|RCC_PERIPHCLK_LPUART1
                              |RCC_PERIPHCLK_RTC;
  PeriphClkInit.Usart1ClockSelection = RCC_USART1CLKSOURCE_PCLK2;
  PeriphClkInit.Lpuart1ClockSelection = RCC_LPUART1CLKSOURCE_PCLK1;
  PeriphClkInit.RTCClockSelection = RCC_RTCCLKSOURCE_LSI;
  if (HAL_RCCEx_PeriphCLKConfig(&PeriphClkInit) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/**
  * @brief GPIO外部中断回调函数
  * @param GPIO_Pin: 触发中断的引脚
  * @retval None
  */
void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin)
{
  // 检查中断使能标志，如果禁止则直接返回
  if (!interrupt_enabled) {
    return;
  }

  if (GPIO_Pin == INT1_Pin)
  {
    printf("INT1 Wake Trigger!\r\n");
    interrupt_enabled = 0;  // 禁止后续触发，直到下次休眠前重新使能
  }
  else if (GPIO_Pin == INT2_Pin)
  {
    printf("INT2 Wake Trigger!\r\n");
    interrupt_enabled = 0;  // 禁止后续触发，直到下次休眠前重新使能
  }
}



/* USER CODE END 4 */

/**
  * @brief  Period elapsed callback in non blocking mode
  * @note   This function is called  when TIM6 interrupt took place, inside
  * HAL_TIM_IRQHandler(). It makes a direct call to HAL_IncTick() to increment
  * a global variable "uwTick" used as application time base.
  * @param  htim : TIM handle
  * @retval None
  */
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
  /* USER CODE BEGIN Callback 0 */

  /* USER CODE END Callback 0 */
  if (htim->Instance == TIM6)
  {
    HAL_IncTick();
  }
  /* USER CODE BEGIN Callback 1 */

  /* USER CODE END Callback 1 */
}

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}
#ifdef USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
