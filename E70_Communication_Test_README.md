# E70通信测试说明

## 测试功能概述

已在main.c中添加E70设备间通信测试代码，用于验证两个设备之间的通信是否正常。

## 测试数据包定义

### 主设备（当前设备）
- **发送数据**: `FC FD 55 66 FD` (每3秒发送一次)
- **期望接收**: `FC FD 77 88 FD`

### 从设备（通信端设备）
- **发送数据**: `FC FD 77 88 FD` (每3秒发送一次)  
- **期望接收**: `FC FD 55 66 FD`

## 测试流程

1. **初始化阶段**
   - E70模块完成基础配置
   - 启动连续接收模式
   - 显示测试开始信息

2. **通信测试循环**
   - 每3秒自动发送测试数据包
   - 实时检查接收到的数据
   - 打印发送和接收状态

## 串口输出信息

### 正常通信时的输出
```
=== E70 Communication Test Started ===
Send: FC FD 55 66 FD (every 3s)
Expect: FC FD 77 88 FD
Test Send: FC FD 55 66 FD
Test Recv OK: FC FD 77 88 FD
```

### 异常情况输出
```
Test Send Failed!                    // 发送失败
Test Recv Error: FC FD 11 22 FD     // 接收到错误数据
```

## 测试验证方法

1. **单设备测试**: 可以使用串口工具手动发送 `FC FD 77 88 FD` 来验证接收功能
2. **双设备测试**: 将代码烧录到两个设备，修改其中一个设备的数据包定义
3. **通信距离测试**: 逐步增加两设备间距离，观察通信质量

## 代码修改说明

### 新增变量
- `test_send_timer`: 发送计时器
- `test_last_send_time`: 上次发送时间记录
- `test_comm_enabled`: 通信测试使能标志
- `test_send_packet[]`: 发送数据包数组
- `test_recv_packet[]`: 期望接收数据包数组

### 新增函数
- `E70_TestSendData()`: 发送测试数据
- `E70_TestCheckReceive()`: 检查接收数据
- `E70_TestCommunication()`: 测试主循环

### 主循环修改
- 添加了E70通信测试调用
- 增加10ms延时避免CPU占用过高

## 注意事项

1. **E70模块状态**: 确保E70模块已正确初始化并处于通信模式
2. **串口配置**: 确保LPUART1配置正确（9600波特率）
3. **天线连接**: 确保天线正确连接，影响通信距离和质量
4. **电源供应**: 确保E70模块电源稳定
5. **环境干扰**: 避免在强电磁干扰环境下测试

## 故障排除

1. **无法发送**: 检查E70模块电源和串口连接
2. **无法接收**: 检查接收模式是否正确启动
3. **数据错误**: 检查两设备的数据包定义是否匹配
4. **通信距离短**: 检查天线连接和发射功率设置

## 后续扩展

测试验证通信正常后，可以基于此框架扩展：
- 添加数据校验机制
- 实现双向确认协议
- 增加通信质量统计
- 集成到实际业务流程中
