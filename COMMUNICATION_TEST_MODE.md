# 通信测试模式说明

## 概述
为了方便调试E70模块通信，在原有状态机前面添加了通信测试模式。在测试模式下，状态机暂时失效，设备会持续进行通信测试。

## 修改内容

### 1. 主要文件修改
- `Src/main.c`: 添加通信测试模式逻辑
- `Src/e70_config.c`: 添加接收数据打印函数
- `Inc/e70_config.h`: 添加函数声明

### 2. 新增变量
```c
// 通信测试模式控制变量
static uint8_t communication_test_mode = 1;    // 1=测试模式，0=正常状态机模式
static uint32_t last_send_time = 0;            // 上次发送时间
```

### 3. 测试模式功能
- **发送频率**: 每1秒发送一次测试数据
- **发送内容**: `FC FD 55 66 FD` (5字节)
- **接收监听**: 持续监听串口数据
- **数据打印**: 实时打印接收到的原始数据

### 4. 期望接收数据格式
另一台设备应发送: `FA FB + ID(2字节) + 电压(2字节) + FD` = 8字节
- `FA FB`: 数据包头
- `ID`: 2字节设备ID (大端序)
- `电压`: 2字节电压值 (大端序，单位mV)
- `FD`: 数据包尾

## 使用方法

### 切换到测试模式
```c
static uint8_t communication_test_mode = 1;  // 设置为1启用测试模式
```

### 切换到正常模式
```c
static uint8_t communication_test_mode = 0;  // 设置为0启用正常状态机
```

## 测试输出示例

### 启动时输出
```
=== COMMUNICATION TEST MODE ENABLED ===
Will send FC FD 55 66 FD every 1 second
Expecting to receive FA FB + ID(2) + Voltage(2) + FD
To disable test mode, set communication_test_mode = 0
```

### 运行时输出
```
=== Communication Test Mode ===
Sending test packet: FC FD 55 66 FD
Received 8 bytes: FA FB 00 02 0F A0 FD
Parsed: Device ID=0x0002, Voltage=4000mV
```

## 注意事项

1. **E70初始化保留**: 测试模式仍会执行E70的初始化配置
2. **状态机暂停**: 测试模式下状态机完全不执行
3. **持续通信**: 测试模式会持续发送和接收，便于调试
4. **缓冲区管理**: 接收缓冲区满时会自动重置
5. **实时打印**: 所有接收到的数据都会实时打印，包括原始十六进制和解析后的内容

## 调试建议

1. 首先确认两台设备都能正常启动
2. 观察发送方是否正常发送测试数据
3. 观察接收方是否能收到并正确解析数据
4. 确认通信正常后，将 `communication_test_mode` 设置为 0 恢复正常功能
